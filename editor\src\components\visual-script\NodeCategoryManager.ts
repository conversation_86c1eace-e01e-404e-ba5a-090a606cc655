/**
 * 节点分类管理器
 * 统一管理所有视觉脚本节点的分类和显示
 */
import { useTranslation } from 'react-i18next';

/**
 * 节点分类接口
 */
export interface NodeCategory {
  id: string;
  name: string;
  displayName: string;
  description: string;
  icon: string;
  color: string;
  order: number;
  parent?: string;
  children?: string[];
  tags: string[];
  isVisible: boolean;
  isExpanded: boolean;
}

/**
 * 节点分类配置
 */
export interface NodeCategoryConfig {
  id: string;
  nameKey: string;
  descriptionKey?: string;
  icon: string;
  color: string;
  order: number;
  parent?: string;
  tags?: string[];
  isVisible?: boolean;
}

/**
 * 节点分类管理器类
 */
export class NodeCategoryManager {
  private static instance: NodeCategoryManager;
  private categories: Map<string, NodeCategory> = new Map();
  private categoryHierarchy: Map<string, string[]> = new Map();
  private t: any;

  private constructor() {
    this.initializeDefaultCategories();
  }

  public static getInstance(): NodeCategoryManager {
    if (!NodeCategoryManager.instance) {
      NodeCategoryManager.instance = new NodeCategoryManager();
    }
    return NodeCategoryManager.instance;
  }

  /**
   * 设置翻译函数
   */
  public setTranslation(t: any): void {
    this.t = t;
    this.updateCategoryNames();
  }

  /**
   * 初始化默认分类
   */
  private initializeDefaultCategories(): void {
    const defaultCategories: NodeCategoryConfig[] = [
      // 基础分类
      { id: 'events', nameKey: '节点分类.events', icon: 'event', color: '#FF5722', order: 1 },
      { id: 'flow', nameKey: '节点分类.flow', icon: 'call_split', color: '#9C27B0', order: 2 },
      { id: 'logic', nameKey: '节点分类.logic', icon: 'psychology', color: '#3F51B5', order: 3 },
      { id: 'math', nameKey: '节点分类.math', icon: 'calculate', color: '#2196F3', order: 4 },
      { id: 'string', nameKey: '节点分类.string', icon: 'text_fields', color: '#00BCD4', order: 5 },
      { id: 'debug', nameKey: '节点分类.debug', icon: 'bug_report', color: '#F44336', order: 6 },
      
      // 实体和变换
      { id: 'entity', nameKey: '节点分类.entity', icon: 'view_in_ar', color: '#4CAF50', order: 7 },
      { id: 'transform', nameKey: '节点分类.transform', icon: 'transform', color: '#8BC34A', order: 8 },
      
      // 物理和动画
      { id: 'physics', nameKey: '节点分类.physics', icon: 'sports_soccer', color: '#CDDC39', order: 9 },
      { id: 'animation', nameKey: '节点分类.animation', icon: 'animation', color: '#FFEB3B', order: 10 },
      
      // 音频和输入
      { id: 'audio', nameKey: '节点分类.audio', icon: 'volume_up', color: '#FFC107', order: 11 },
      { id: 'input', nameKey: '节点分类.input', icon: 'input', color: '#FF9800', order: 12 },
      
      // UI和自定义
      { id: 'ui', nameKey: '节点分类.ui', icon: 'web', color: '#FF5722', order: 13 },
      { id: 'custom', nameKey: '节点分类.custom', icon: 'extension', color: '#795548', order: 14 },

      // 批次0.1渲染系统分类
      { id: 'rendering', nameKey: 'batch01Nodes.categories.rendering.name', icon: 'palette', color: '#E91E63', order: 15, tags: ['batch01', 'rendering'] },
      
      // 材质分类
      { id: 'Material/Core', nameKey: 'batch01Nodes.categories.Material/Core.name', icon: 'palette', color: '#FF9800', order: 16, parent: 'rendering', tags: ['batch01', 'material', 'core'] },
      { id: 'Material/Editor', nameKey: 'batch01Nodes.categories.Material/Editor.name', icon: 'edit', color: '#FFC107', order: 17, parent: 'rendering', tags: ['batch01', 'material', 'editor'] },
      { id: 'Material/Advanced', nameKey: 'batch01Nodes.categories.Material/Advanced.name', icon: 'experiment', color: '#FFEB3B', order: 18, parent: 'rendering', tags: ['batch01', 'material', 'advanced'] },
      
      // 后处理分类
      { id: 'Rendering/PostProcess', nameKey: 'batch01Nodes.categories.Rendering/PostProcess.name', icon: 'filter', color: '#E91E63', order: 19, parent: 'rendering', tags: ['batch01', 'postprocess', 'effect'] },
      
      // 着色器分类
      { id: 'Shader/Core', nameKey: 'batch01Nodes.categories.Shader/Core.name', icon: 'code', color: '#9C27B0', order: 20, parent: 'rendering', tags: ['batch01', 'shader', 'core'] },
      { id: 'Shader/Advanced', nameKey: 'batch01Nodes.categories.Shader/Advanced.name', icon: 'function', color: '#9C27B0', order: 21, parent: 'rendering', tags: ['batch01', 'shader', 'advanced'] },
      { id: 'Shader/Debug', nameKey: 'batch01Nodes.categories.Shader/Debug.name', icon: 'bug', color: '#F44336', order: 22, parent: 'rendering', tags: ['batch01', 'shader', 'debug'] },
      { id: 'Shader/Utility', nameKey: 'batch01Nodes.categories.Shader/Utility.name', icon: 'tool', color: '#009688', order: 23, parent: 'rendering', tags: ['batch01', 'shader', 'utility'] },
      
      // 渲染优化分类
      { id: 'Rendering/Optimization', nameKey: 'batch01Nodes.categories.Rendering/Optimization.name', icon: 'speed', color: '#4CAF50', order: 24, parent: 'rendering', tags: ['batch01', 'optimization', 'performance'] },
      { id: 'Rendering/Analysis', nameKey: 'batch01Nodes.categories.Rendering/Analysis.name', icon: 'analytics', color: '#4CAF50', order: 25, parent: 'rendering', tags: ['batch01', 'analysis', 'performance'] },
      { id: 'Rendering/Pipeline', nameKey: 'batch01Nodes.categories.Rendering/Pipeline.name', icon: 'linear_scale', color: '#4CAF50', order: 26, parent: 'rendering', tags: ['batch01', 'pipeline', 'rendering'] }
    ];

    defaultCategories.forEach(config => {
      this.addCategory(config);
    });

    this.buildCategoryHierarchy();
  }

  /**
   * 添加分类
   */
  public addCategory(config: NodeCategoryConfig): void {
    const category: NodeCategory = {
      id: config.id,
      name: config.id,
      displayName: config.nameKey,
      description: config.descriptionKey || '',
      icon: config.icon,
      color: config.color,
      order: config.order,
      parent: config.parent,
      children: [],
      tags: config.tags || [],
      isVisible: config.isVisible !== false,
      isExpanded: false
    };

    this.categories.set(config.id, category);
  }

  /**
   * 构建分类层次结构
   */
  private buildCategoryHierarchy(): void {
    this.categoryHierarchy.clear();

    for (const [id, category] of this.categories.entries()) {
      if (category.parent) {
        const siblings = this.categoryHierarchy.get(category.parent) || [];
        siblings.push(id);
        this.categoryHierarchy.set(category.parent, siblings);
        
        // 更新父分类的children
        const parentCategory = this.categories.get(category.parent);
        if (parentCategory) {
          parentCategory.children = siblings;
        }
      }
    }
  }

  /**
   * 更新分类名称（使用翻译）
   */
  private updateCategoryNames(): void {
    if (!this.t) return;

    for (const [id, category] of this.categories.entries()) {
      try {
        category.displayName = this.t(category.displayName) || category.name;
        if (category.description) {
          category.description = this.t(category.description) || category.description;
        }
      } catch (error) {
        console.warn(`翻译分类 ${id} 失败:`, error);
        category.displayName = category.name;
      }
    }
  }

  /**
   * 获取所有分类
   */
  public getAllCategories(): NodeCategory[] {
    return Array.from(this.categories.values()).sort((a, b) => a.order - b.order);
  }

  /**
   * 获取顶级分类
   */
  public getTopLevelCategories(): NodeCategory[] {
    return this.getAllCategories().filter(category => !category.parent);
  }

  /**
   * 获取子分类
   */
  public getChildCategories(parentId: string): NodeCategory[] {
    const childIds = this.categoryHierarchy.get(parentId) || [];
    return childIds.map(id => this.categories.get(id)).filter(Boolean) as NodeCategory[];
  }

  /**
   * 获取分类
   */
  public getCategory(id: string): NodeCategory | undefined {
    return this.categories.get(id);
  }

  /**
   * 搜索分类
   */
  public searchCategories(query: string, tags?: string[]): NodeCategory[] {
    const lowerQuery = query.toLowerCase();
    
    return this.getAllCategories().filter(category => {
      // 名称匹配
      const nameMatch = category.name.toLowerCase().includes(lowerQuery) ||
                       category.displayName.toLowerCase().includes(lowerQuery);
      
      // 描述匹配
      const descMatch = category.description.toLowerCase().includes(lowerQuery);
      
      // 标签匹配
      const tagMatch = !tags || tags.length === 0 || 
                      tags.some(tag => category.tags.includes(tag));
      
      return (nameMatch || descMatch) && tagMatch && category.isVisible;
    });
  }

  /**
   * 切换分类展开状态
   */
  public toggleCategoryExpansion(id: string): void {
    const category = this.categories.get(id);
    if (category) {
      category.isExpanded = !category.isExpanded;
    }
  }

  /**
   * 设置分类可见性
   */
  public setCategoryVisibility(id: string, visible: boolean): void {
    const category = this.categories.get(id);
    if (category) {
      category.isVisible = visible;
    }
  }

  /**
   * 获取分类路径
   */
  public getCategoryPath(id: string): string[] {
    const path: string[] = [];
    let currentId: string | undefined = id;
    
    while (currentId) {
      const category = this.categories.get(currentId);
      if (category) {
        path.unshift(category.displayName);
        currentId = category.parent;
      } else {
        break;
      }
    }
    
    return path;
  }

  /**
   * 获取批次0.1分类
   */
  public getBatch01Categories(): NodeCategory[] {
    return this.getAllCategories().filter(category => 
      category.tags.includes('batch01')
    );
  }

  /**
   * 获取渲染相关分类
   */
  public getRenderingCategories(): NodeCategory[] {
    return this.getAllCategories().filter(category => 
      category.tags.includes('rendering') || category.parent === 'rendering'
    );
  }

  /**
   * 重置分类状态
   */
  public resetCategoryStates(): void {
    for (const category of this.categories.values()) {
      category.isExpanded = false;
    }
  }

  /**
   * 导出分类配置
   */
  public exportCategoryConfig(): any {
    return {
      categories: Array.from(this.categories.values()),
      hierarchy: Object.fromEntries(this.categoryHierarchy.entries()),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 获取分类统计信息
   */
  public getCategoryStatistics(): any {
    const stats = {
      totalCategories: this.categories.size,
      topLevelCategories: this.getTopLevelCategories().length,
      batch01Categories: this.getBatch01Categories().length,
      renderingCategories: this.getRenderingCategories().length,
      visibleCategories: this.getAllCategories().filter(c => c.isVisible).length,
      expandedCategories: this.getAllCategories().filter(c => c.isExpanded).length
    };

    return stats;
  }
}

/**
 * React Hook for using NodeCategoryManager
 */
export const useNodeCategoryManager = () => {
  const { t } = useTranslation();
  const manager = NodeCategoryManager.getInstance();
  
  // 设置翻译函数
  manager.setTranslation(t);
  
  return manager;
};

export default NodeCategoryManager;
