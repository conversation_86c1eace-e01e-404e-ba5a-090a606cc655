/**
 * 批次0.1节点集成验证脚本
 * 验证场景管理和资源管理节点的注册和集成状态
 */

const fs = require('fs');
const path = require('path');

// 预期的节点列表
const EXPECTED_SCENE_NODES = [
  // 场景基础管理节点（7个）
  'LoadSceneNode',
  'SaveSceneNode', 
  'CreateSceneNode',
  'DestroySceneNode',
  'AddObjectToSceneNode',
  'RemoveObjectFromSceneNode',
  'FindSceneObjectNode',
  
  // 场景编辑节点（15个）
  'SceneViewportNode',
  'ObjectSelectionNode',
  'ObjectTransformNode',
  'ObjectDuplicationNode',
  'ObjectGroupingNode',
  'ObjectLayerNode',
  'GridSnapNode',
  'ObjectAlignmentNode',
  'ObjectDistributionNode',
  'UndoRedoNode',
  'HistoryManagementNode',
  'SelectionFilterNode',
  'ViewportNavigationNode',
  'ViewportRenderingNode',
  'ViewportSettingsNode',
  
  // 场景切换节点（1个）
  'SceneTransitionNode',
  
  // 场景生成节点（10个）
  'AutoSceneGenerationNode',
  'SceneLayoutNode',
  'ProceduralTerrainNode',
  'VegetationGeneratorNode',
  'BuildingGeneratorNode',
  'RoadNetworkNode',
  'WeatherSystemNode',
  'LightingSetupNode',
  'AtmosphereNode',
  'SceneOptimizationNode'
];

const EXPECTED_RESOURCE_NODES = [
  // 资源加载节点（12个）
  'LoadAssetNode',
  'UnloadAssetNode',
  'PreloadAssetNode',
  'AsyncLoadAssetNode',
  'LoadAssetBundleNode',
  'AssetDependencyNode',
  'AssetCacheNode',
  'AssetCompressionNode',
  'AssetEncryptionNode',
  'AssetValidationNode',
  'AssetMetadataNode',
  'AssetVersionNode',
  
  // 资源优化节点（10个）
  'AssetOptimizationNode',
  'TextureCompressionNode',
  'MeshOptimizationNode',
  'AudioCompressionNode',
  'AssetBatchingNode',
  'AssetStreamingNode',
  'AssetMemoryManagementNode',
  'AssetGarbageCollectionNode',
  'AssetPerformanceMonitorNode',
  'AssetUsageAnalyticsNode'
];

class Batch01IntegrationValidator {
  constructor() {
    this.results = {
      sceneNodes: { expected: 33, found: 0, missing: [], errors: [] },
      resourceNodes: { expected: 22, found: 0, missing: [], errors: [] },
      registry: { valid: false, errors: [] },
      editor: { integrated: false, errors: [] }
    };
  }

  /**
   * 验证节点注册表
   */
  validateNodeRegistry() {
    console.log('🔍 验证节点注册表...');
    
    try {
      const registryPath = path.resolve('engine/src/visual-script/registry/NodeRegistry.ts');
      if (!fs.existsSync(registryPath)) {
        this.results.registry.errors.push('NodeRegistry.ts 文件不存在');
        return false;
      }
      
      const registryContent = fs.readFileSync(registryPath, 'utf8');
      
      // 检查批次0.1注册方法
      if (!registryContent.includes('registerBatch01Nodes')) {
        this.results.registry.errors.push('缺少 registerBatch01Nodes 方法');
      }
      
      if (!registryContent.includes('registerSceneManagementNodes')) {
        this.results.registry.errors.push('缺少 registerSceneManagementNodes 方法');
      }
      
      if (!registryContent.includes('registerResourceManagementNodes')) {
        this.results.registry.errors.push('缺少 registerResourceManagementNodes 方法');
      }
      
      // 检查场景节点注册
      this.validateSceneNodesInRegistry(registryContent);
      
      // 检查资源节点注册
      this.validateResourceNodesInRegistry(registryContent);
      
      this.results.registry.valid = this.results.registry.errors.length === 0;
      
      console.log(`✅ 节点注册表验证完成`);
      return this.results.registry.valid;
      
    } catch (error) {
      this.results.registry.errors.push(`验证失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 验证场景节点在注册表中的存在
   */
  validateSceneNodesInRegistry(registryContent) {
    console.log('🔍 验证场景管理节点注册...');
    
    EXPECTED_SCENE_NODES.forEach(nodeType => {
      if (registryContent.includes(`type: ${nodeType}.TYPE`) || 
          registryContent.includes(`type: '${nodeType}'`)) {
        this.results.sceneNodes.found++;
      } else {
        this.results.sceneNodes.missing.push(nodeType);
      }
    });
    
    console.log(`📊 场景节点: ${this.results.sceneNodes.found}/${this.results.sceneNodes.expected} 已注册`);
  }

  /**
   * 验证资源节点在注册表中的存在
   */
  validateResourceNodesInRegistry(registryContent) {
    console.log('🔍 验证资源管理节点注册...');
    
    EXPECTED_RESOURCE_NODES.forEach(nodeType => {
      if (registryContent.includes(`type: ${nodeType}.TYPE`) || 
          registryContent.includes(`type: '${nodeType}'`)) {
        this.results.resourceNodes.found++;
      } else {
        this.results.resourceNodes.missing.push(nodeType);
      }
    });
    
    console.log(`📊 资源节点: ${this.results.resourceNodes.found}/${this.results.resourceNodes.expected} 已注册`);
  }

  /**
   * 验证编辑器集成
   */
  validateEditorIntegration() {
    console.log('🔍 验证编辑器集成...');
    
    try {
      // 检查编辑器节点面板配置
      const editorConfigPaths = [
        'editor/src/components/visual-script/NodePanel.vue',
        'editor/src/components/visual-script/nodes/index.ts',
        'editor/src/stores/nodeStore.ts'
      ];
      
      let integrationFound = false;
      
      editorConfigPaths.forEach(configPath => {
        if (fs.existsSync(path.resolve(configPath))) {
          const content = fs.readFileSync(path.resolve(configPath), 'utf8');
          if (content.includes('SCENE_MANAGEMENT') || 
              content.includes('RESOURCE_MANAGEMENT') ||
              content.includes('场景管理') ||
              content.includes('资源管理')) {
            integrationFound = true;
          }
        }
      });
      
      this.results.editor.integrated = integrationFound;
      
      if (!integrationFound) {
        this.results.editor.errors.push('编辑器中未找到场景管理或资源管理节点集成');
      }
      
      console.log(`✅ 编辑器集成验证完成`);
      return this.results.editor.integrated;
      
    } catch (error) {
      this.results.editor.errors.push(`编辑器集成验证失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 生成验证报告
   */
  generateReport() {
    console.log('\n📋 批次0.1节点集成验证报告');
    console.log('='.repeat(50));
    
    // 场景节点报告
    console.log(`\n🎬 场景管理节点 (${this.results.sceneNodes.found}/${this.results.sceneNodes.expected})`);
    if (this.results.sceneNodes.missing.length > 0) {
      console.log('❌ 缺失节点:');
      this.results.sceneNodes.missing.forEach(node => console.log(`   - ${node}`));
    } else {
      console.log('✅ 所有场景节点已注册');
    }
    
    // 资源节点报告
    console.log(`\n📦 资源管理节点 (${this.results.resourceNodes.found}/${this.results.resourceNodes.expected})`);
    if (this.results.resourceNodes.missing.length > 0) {
      console.log('❌ 缺失节点:');
      this.results.resourceNodes.missing.forEach(node => console.log(`   - ${node}`));
    } else {
      console.log('✅ 所有资源节点已注册');
    }
    
    // 注册表报告
    console.log(`\n📝 节点注册表: ${this.results.registry.valid ? '✅ 有效' : '❌ 无效'}`);
    if (this.results.registry.errors.length > 0) {
      console.log('❌ 注册表错误:');
      this.results.registry.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // 编辑器集成报告
    console.log(`\n🖥️ 编辑器集成: ${this.results.editor.integrated ? '✅ 已集成' : '❌ 未集成'}`);
    if (this.results.editor.errors.length > 0) {
      console.log('❌ 集成错误:');
      this.results.editor.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // 总结
    const totalExpected = this.results.sceneNodes.expected + this.results.resourceNodes.expected;
    const totalFound = this.results.sceneNodes.found + this.results.resourceNodes.found;
    const completionRate = ((totalFound / totalExpected) * 100).toFixed(1);
    
    console.log(`\n📈 总体完成度: ${totalFound}/${totalExpected} (${completionRate}%)`);
    
    if (completionRate === '100.0' && this.results.registry.valid) {
      console.log('🎉 批次0.1节点集成验证通过！');
      return true;
    } else {
      console.log('⚠️ 批次0.1节点集成需要进一步完善');
      return false;
    }
  }

  /**
   * 运行完整验证
   */
  async run() {
    console.log('🚀 开始批次0.1节点集成验证...\n');
    
    const registryValid = this.validateNodeRegistry();
    const editorIntegrated = this.validateEditorIntegration();
    
    const success = this.generateReport();
    
    console.log('\n' + '='.repeat(50));
    console.log(success ? '✅ 验证完成' : '❌ 验证失败');
    
    return success;
  }
}

// 运行验证
if (require.main === module) {
  const validator = new Batch01IntegrationValidator();
  validator.run().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('验证过程中发生错误:', error);
    process.exit(1);
  });
}

module.exports = Batch01IntegrationValidator;
